import { Doughnut } from 'react-chartjs-2'
import { Chart as ChartJS, ArcElement, Toolt<PERSON>, Legend } from 'chart.js'
ChartJS.register(Arc<PERSON>lement, Tooltip, Legend)

type Props = { labels: string[]; dataPoints: number[]; centerText?: string }

export default function Donut({ labels, dataPoints, centerText }: Props) {
  const data = { labels, datasets: [{ data: dataPoints, borderWidth: 0 }] }
  const options = { plugins: { legend: { position: 'right' as const } }, cutout: '70%', maintainAspectRatio: false }
  return (
    <div className="relative h-64">
      <Doughnut data={data} options={options} />
      {centerText && <div className="absolute inset-0 grid place-items-center text-lg font-semibold">{centerText}</div>}
    </div>
  )
}
