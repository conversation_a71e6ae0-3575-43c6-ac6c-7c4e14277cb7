import { Line } from 'react-chartjs-2'
import {
  Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Tooltip, <PERSON>ller, Legend,
} from 'chart.js'
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Tooltip, Filler, Legend)

export type LineDataset = {
  label: string
  data: number[]
  borderColor?: string
  backgroundColor?: string
}

type Props = { labels: string[]; datasets: LineDataset[]; height?: number }

export default function AreaLine({ labels, datasets, height = 260 }: Props) {
  const data = {
    labels,
    datasets: datasets.map(ds => ({ ...ds, fill: true, tension: 0.35, borderWidth: 2 })),
  }
  const options = {
    plugins: { legend: { display: true, position: 'bottom' as const } },
    scales: { x: { grid: { display: false } }, y: { grid: { color: 'rgba(0,0,0,.06)' } } },
    maintainAspectRatio: false,
  }
  return <div style={{ height }}><Line data={data} options={options} /></div>
}
