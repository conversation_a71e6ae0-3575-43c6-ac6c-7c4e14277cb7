import { Bar as Bar<PERSON><PERSON> } from 'react-chartjs-2'
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Tooltip, Legend } from 'chart.js'
ChartJS.register(CategoryScale, LinearScale, BarElement, Tooltip, Legend)

type Props = { labels: string[]; dataPoints: number[]; height?: number }

export default function Bar({ labels, dataPoints, height = 260 }: Props) {
  const data = { labels, datasets: [{ label: 'Count', data: dataPoints, borderRadius: 8 }] }
  const options = {
    plugins: { legend: { display: false } },
    scales: { x: { grid: { display: false } }, y: { grid: { color: 'rgba(0,0,0,.06)' } } },
    maintainAspectRatio: false,
  }
  return <div style={{ height }}><BarChart data={data} options={options} /></div>
}
