import { Bar } from 'react-chartjs-2'
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Tooltip, Legend } from 'chart.js'
ChartJS.register(CategoryScale, LinearScale, BarElement, Tooltip, Legend)

type Props = { labels: string[]; dataPoints: number[]; height?: number }

export default function BarY({ labels, dataPoints, height = 260 }: Props) {
  const data = { labels, datasets: [{ label: 'Value', data: dataPoints, borderRadius: 8 }] }
  const options = {
    indexAxis: 'y' as const,
    plugins: { legend: { display: false } },
    scales: { x: { grid: { color: 'rgba(0,0,0,.06)' } }, y: { grid: { display: false } } },
    maintainAspectRatio: false,
  }
  return <div style={{ height }}><Bar data={data} options={options} /></div>
}
