import React from 'react'

type Props = {
  id?: string
  title: string
  children: React.ReactNode
  actionText?: string
  onAction?: () => void
  className?: string
}

export default function SectionCard({ id, title, children, actionText = 'View Details', onAction, className = '' }: Props) {
  return (
    <section id={id} className={`bg-white rounded-2xl border shadow-sm p-4 ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-semibold">{title}</h3>
        <button className="text-sm text-blue-600" onClick={onAction}>{actionText}</button>
      </div>
      {children}
    </section>
  )
}
