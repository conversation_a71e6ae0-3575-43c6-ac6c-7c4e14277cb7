type StatCardProps = {
  icon?: React.ReactNode
  title: string
  value: string | number
  trend?: number // positive/negative percentage
  className?: string
  iconColor?: string // for different colored icon backgrounds
}

export default function StatCard({ icon, title, value, trend, className = '', iconColor = 'bg-red-50' }: StatCardProps) {
  return (
    <div className={`bg-white rounded-xl border border-gray-200 shadow-sm p-4 hover:shadow-md transition-shadow ${className}`}>
      <div className="flex items-center gap-3">
        {/* Icon Container */}
        <div className="flex-shrink-0">
          <div className={`w-12 h-12 rounded-lg ${iconColor} flex items-center justify-center`}>
            <div className="w-6 h-6 text-red-500">
              {icon || (
                <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
                  <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                </svg>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-500 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>

        {/* Trend Indicator */}
        {trend !== undefined && (
          <div className="flex-shrink-0">
            <span className={`text-xs font-medium px-2 py-1 rounded-full ${
              trend > 0
                ? 'text-emerald-700 bg-emerald-50'
                : 'text-red-700 bg-red-50'
            }`}>
              {trend > 0 ? '↗' : '↘'} {Math.abs(trend)}%
            </span>
          </div>
        )}
      </div>
    </div>
  )
}
