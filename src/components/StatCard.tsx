type StatCardProps = {
  icon?: React.ReactNode
  title: string
  value: string | number
  trend?: number // positive/negative percentage
  className?: string
}

export default function StatCard({ icon, title, value, trend, className = '' }: StatCardProps) {
  return (
   <div className={`bg-white rounded-xl border border-gray-200 shadow-sm p-6 hover:shadow-md transition-shadow ${className}`}>
      <div className="flex items-start justify-between">
        {/* Icon Container */}
        <div className="flex-shrink-0">
          <div className="w-12 h-12 rounded-lg bg-red-50 flex items-center justify-center">
            <div className="w-6 h-6">
              {icon}
            </div>
          </div>
        </div>
        
        {/* Trend Indicator */}
        {trend !== undefined && (
          <span className={`text-xs font-medium px-2 py-1 rounded-full ${
            trend > 0 
              ? 'text-emerald-700 bg-emerald-50' 
              : 'text-red-700 bg-red-50'
          }`}>
            {trend > 0 ? '↗' : '↘'} {Math.abs(trend)}%
          </span>
        )}
      </div>
      
      {/* Content */}
      <div className="mt-4">
        <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
        <p className="text-2xl font-bold text-gray-900">{value}</p>
      </div>
    </div>

  )
}
