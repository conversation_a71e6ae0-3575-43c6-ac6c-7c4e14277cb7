type Status =
  | 'Out for Delivery'
  | 'Ready to Pickup'
  | 'Preparing'
  | 'Cancelled'
  | 'Delivered'
  | 'Failed'

export default function StatusPill({ status }: { status: Status }) {
  const map: Record<Status, string> = {
    'Out for Delivery': 'bg-emerald-100 text-emerald-700',
    'Ready to Pickup': 'bg-blue-100 text-blue-700',
    'Preparing': 'bg-amber-100 text-amber-800',
    'Cancelled': 'bg-red-100 text-red-700',
    'Delivered': 'bg-emerald-100 text-emerald-700',
    'Failed': 'bg-gray-200 text-gray-700'
  }
  const icon: Record<Status, string> = {
    'Out for Delivery': '🚚',
    'Ready to Pickup': '📦',
    'Preparing': '🧑‍🍳',
    'Cancelled': '⛔',
    'Delivered': '✅',
    'Failed': '⚠️'
  }
  return (
    <span className={`inline-flex items-center gap-1 rounded-full px-2.5 py-1 text-xs ${map[status]}`}>
      <span>{icon[status]}</span>
      {status}
    </span>
  )
}
