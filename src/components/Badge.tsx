type Props = { tone?: 'success'|'danger'|'muted'|'warning'; children: React.ReactNode }
export default function Badge({ tone='muted', children }: Props) {
  const map = {
    success: 'bg-emerald-50 text-emerald-700 border-emerald-200',
    danger:  'bg-red-50 text-red-700 border-red-200',
    warning: 'bg-amber-50 text-amber-700 border-amber-200',
    muted:   'bg-gray-50 text-gray-700 border-gray-200',
  } as const
  return <span className={`inline-flex items-center gap-1 rounded-full border px-2.5 py-1 text-xs ${map[tone]}`}>{children}</span>
}
