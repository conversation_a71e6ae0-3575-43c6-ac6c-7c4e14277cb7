import React, { useEffect } from 'react'

type ModalProps = {
  open: boolean
  onClose: () => void
  title?: string
  children: React.ReactNode
  widthClass?: string // e.g. "max-w-lg"
}

export default function Modal({ open, onClose, title, children, widthClass = 'max-w-lg' }: ModalProps) {
  useEffect(() => {
    if (!open) return
    const onEsc = (e: KeyboardEvent) => e.key === 'Escape' && onClose()
    document.addEventListener('keydown', onEsc)
    return () => document.removeEventListener('keydown', onEsc)
  }, [open, onClose])

  if (!open) return null

  return (
    <div className="fixed inset-0 z-50">
      <div className="absolute inset-0 bg-black/30" onClick={onClose} />
      <div className="absolute inset-0 p-4 overflow-y-auto">
        <div className={`mx-auto bg-white rounded-[24px] shadow-xl ${widthClass}`}>
          {(title || onClose) && (
            <div className="flex items-center justify-between px-5 py-4 border-b">
              <h3 className="font-semibold">{title}</h3>
              <button onClick={onClose} className="size-8 grid place-items-center rounded-lg hover:bg-gray-100">✕</button>
            </div>
          )}
          <div className="p-5">{children}</div>
        </div>
      </div>
    </div>
  )
}
