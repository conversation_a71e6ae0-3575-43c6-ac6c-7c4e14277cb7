import Sidebar from './Sidebar'
import Header from './Header'

type PageShellProps = {
  children: React.ReactNode
  title?: string
  actions?: React.ReactNode
  showSearch?: boolean
  showTimeFilter?: boolean
  showLocationFilter?: boolean
}

export default function PageShell({
  children,
  title,
  actions,
  showSearch = true,
  showTimeFilter = true,
  showLocationFilter = true
}: PageShellProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        <Sidebar />
        <div className="flex-1">
          <Header
            title={title}
            showSearch={showSearch}
            showTimeFilter={showTimeFilter}
            showLocationFilter={showLocationFilter}
          />
          <main className="p-4 lg:p-6 space-y-5">
            {actions && (
              <div className="flex items-center justify-end">
                <div className="flex items-center gap-2">{actions}</div>
              </div>
            )}
            {children}
          </main>
        </div>
      </div>
    </div>
  )
}
