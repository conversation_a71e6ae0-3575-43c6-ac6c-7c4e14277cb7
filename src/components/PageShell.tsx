import Sidebar from './Sidebar'
import Header from './Header'

type PageShellProps = {
  children: React.ReactNode
  title?: string
  actions?: React.ReactNode
}

export default function PageShell({ children, title, actions }: PageShellProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        <Sidebar />
        <div className="flex-1">
          <Header />
          <main className="p-4 lg:p-6 space-y-5">
            {(title || actions) && (
              <div className="flex items-center justify-between">
                <h1 className="text-xl font-semibold">{title}</h1>
                <div className="flex items-center gap-2">{actions}</div>
              </div>
            )}
            {children}
          </main>
        </div>
      </div>
    </div>
  )
}
