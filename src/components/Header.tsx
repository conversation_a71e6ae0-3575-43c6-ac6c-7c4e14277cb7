import { useState, useEffect, useRef } from 'react'

type HeaderProps = {
  title?: string
  showSearch?: boolean
  showTimeFilter?: boolean
  showLocationFilter?: boolean
}

export default function Header({
  title,
  showSearch = true,
  showTimeFilter = true,
  showLocationFilter = true
}: HeaderProps) {
  const [showUserMenu, setShowUserMenu] = useState(false)
  const userMenuRef = useRef<HTMLDivElement>(null)

  // Close user menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }
    }

    if (showUserMenu) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showUserMenu])

  return (
    <header className="h-16 sticky top-0 z-20 bg-white border-b">
      <div className="h-full px-6 flex items-center justify-between gap-4">
        {/* Left side - Page title */}
        <div className="flex items-center">
          {title && (
            <h1 className="text-xl font-semibold text-gray-900">{title}</h1>
          )}
        </div>

        {/* Right side - Controls */}
        <div className="flex items-center gap-3">
          {/* Search */}
          {showSearch && (
            <div className="relative">
              <input
                placeholder="Search"
                className="w-64 px-4 py-2 pl-10 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />
              <svg className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          )}

          {/* Time Filter */}
          {showTimeFilter && (
            <select className="px-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent bg-white">
              <option>Today</option>
              <option>Yesterday</option>
              <option>Last 7 Days</option>
              <option selected>This Month</option>
              <option>Last 3 Months</option>
              <option>Custom Range</option>
            </select>
          )}

          {/* Location Filter */}
          {showLocationFilter && (
            <select className="px-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent bg-white">
              <option>Bengaluru</option>
              <option>Chennai</option>
              <option>Mumbai</option>
              <option>Delhi</option>
            </select>
          )}

          {/* Notification Bell */}
          <button className="p-2 text-gray-400 hover:text-gray-600 relative">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM10.97 4.97a.75.75 0 0 0-1.08 1.05l-3.99 4.99a.75.75 0 0 0 1.08 1.05l3.99-4.99a.75.75 0 0 0 0-1.05zM15.25 9.75L16.5 8.5m0 0L18 7m-1.5 1.5L18 10m-1.5-1.5L15.25 9.75" />
            </svg>
            <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>

          {/* User Avatar with Dropdown */}
          <div className="relative" ref={userMenuRef}>
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center gap-2 p-1 rounded-lg hover:bg-gray-50"
            >
              <img
                className="w-8 h-8 rounded-full"
                src="https://i.pravatar.cc/64?img=12"
                alt="User avatar"
              />
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {/* User Dropdown Menu */}
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                <div className="px-4 py-2 border-b border-gray-100">
                  <p className="text-sm font-medium text-gray-900">Sovendra</p>
                  <p className="text-xs text-gray-500"><EMAIL></p>
                </div>
                <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Profile</a>
                <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Settings</a>
                <hr className="my-1" />
                <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Sign out</a>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
