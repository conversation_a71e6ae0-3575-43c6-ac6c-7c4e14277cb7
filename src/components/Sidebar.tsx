import { NavLink } from 'react-router-dom'

const items = [
  { to: '/', label: 'Dashboard' },
  { to: '/promotion-marketing', label: 'Promotion & Marketing' },
  { to: '/order-management', label: 'Order management' },
  { to: '/customers', label: 'Customers' },
  { to: '/reports', label: 'Reports' },
] as const

export default function Sidebar() {
  return (
    <aside className="hidden lg:flex w-64 shrink-0 flex-col border-r bg-white">
      <div className="h-16 flex items-center gap-3 px-4 border-b">
        <div className="size-9 rounded-lg bg-red-600 grid place-items-center text-white font-bold">M</div>
        <div className="text-lg font-semibold">MANI’S</div>
      </div>
      <nav className="p-3 space-y-1 text-left">
        {items.map(i => (
          <NavLink
            key={i.to}
            to={i.to}
            className={({ isActive }) =>
              `block rounded-xl px-4 py-2.5 text-sm ${
                isActive ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50'
              }`
            }
          >
            {i.label}
          </NavLink>
        ))}
      </nav>
      {/* <div className="mt-auto p-3 text-xs text-gray-400">v0.1</div> */}
    </aside>
  )
}
