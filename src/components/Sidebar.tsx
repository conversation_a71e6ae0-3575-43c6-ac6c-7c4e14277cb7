import { NavLink } from 'react-router-dom'
import logo from '../assets/logo.png'

const items = [
  { to: '/', label: 'Dashboard' },
  { to: '/promotion-marketing', label: 'Promotion & Marketing' },
  { to: '/order-management', label: 'Order management' },
  { to: '/customers', label: 'Customers' },
  { to: '/reports', label: 'Reports' },
] as const

export default function Sidebar() {
  return (
    <aside className="hidden lg:flex w-64 shrink-0 flex-col border-r bg-white">
      <div className="h-16 flex items-center gap-3 px-4 border-b">
        <img src={logo} alt="MANI'S Logo" className="h-9 w-9 object-contain" />
        <div className="text-lg font-semibold">MANI’S</div>
      </div>
      <nav className="p-3 space-y-1 text-left">
        {items.map(i => (
          <NavLink
            key={i.to}
            to={i.to}
            className={({ isActive }) =>
              `block rounded-xl px-4 py-2.5 text-sm font-medium transition-colors ${
                isActive
                  ? 'bg-red-50 text-red-600 border border-red-100'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`
            }
          >
            {i.label}
          </NavLink>
        ))}
      </nav>
      {/* <div className="mt-auto p-3 text-xs text-gray-400">v0.1</div> */}
    </aside>
  )
}
