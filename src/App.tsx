import { useState } from 'react'
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Dashboard from './pages/Dashboard';
import Reports from './pages/Reports';
import ReportDetail from './pages/report-details/ReportDetail';
import PromotionMarketing from './pages/PromotionMarketing';
import OrderManagement from './pages/OrderManagement';
import Customers from './pages/Customers';
import Login from './pages/auth/Login';
import reactLogo from './assets/react.svg';
import viteLogo from '/vite.svg';
import './App.css'

function App() {
  return (
    <BrowserRouter>
      <Routes>
        {/* public */}
        <Route path="/login" element={<Login />} />

        {/* protected */}
        <Route path="/" element={<Dashboard />} />
        <Route path="/reports" element={<Reports />} />
        <Route path="/reports/:slug" element={<ReportDetail />} />
        <Route path="/promotion-marketing" element={<PromotionMarketing />} />
        <Route path="/order-management" element={<OrderManagement />} />
        <Route path="/customers" element={<Customers />} />
      </Routes>
    </BrowserRouter>
  )
}

export default App
