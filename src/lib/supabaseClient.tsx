import { createClient } from '@supabase/supabase-js'
const url = import.meta.env.VITE_SUPABASE_URL || "https://rjcwmubmrhnpquuspvrv.supabase.co"
const anon = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJqY3dtdWJtcmhucHF1dXNwdnJ2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyODY5NzQsImV4cCI6MjA1Nzg2Mjk3NH0.89_hJ4w2fxejOAns6cSrnFisboV6041l2a0josP_N30"
export const supabase = createClient(url, anon, {
  auth: { persistSession: true, autoRefreshToken: true, detectSessionInUrl: true }
})
