import PageShell from '../components/PageShell'
import StatCard from '../components/StatCard'
import AreaLine from '../components/charts/AreaLine'
import Donut from '../components/charts/Donut'
import Bar from '../components/charts/Bar'
import totalOrderImg from "../assets/total-orders.png"
import preparingImg from "../assets/preparing.png"
import deliveryImg from "../assets/delivered.png";
import cancelledImg from "../assets/cancelled.png";
import totalRevenueImg from "../assets/total-revenue.png";
import orderAbandonImg from "../assets/order-abundant.png";
import activeCouponsIMg from "../assets/active-coupons.png";
import newUsersImg from "../assets/delivered.png";


export default function Dashboard() {
  const stats = [
    { title: 'Total Orders', value: 312, iconUrl: totalOrderImg },
    { title: 'Preparing', value: 87, iconUrl: preparingImg },
    { title: 'Delivered', value: 39, iconUrl: deliveryImg },
    { title: 'Cancelled', value: 10, iconUrl: cancelledImg },
    { title: 'Total Revenue', value: '₹47,890', iconUrl: totalRevenueImg },
    { title: 'Order Abandon', value: 32, iconUrl: orderAbandonImg },
    { title: 'Active Coupons', value: 12, iconUrl: activeCouponsIMg },
    { title: 'New Users', value: 145, iconUrl: newUsersImg },
  ]

  const days = ['Jul 1', 'Jul 4', 'Jul 7', 'Jul 10', 'Jul 13']
  const areaSets = [
    { label: 'Returning', data: [22, 65, 40, 60, 90] },
    { label: 'New', data: [12, 25, 18, 28, 42] },
  ]

  return (
    <PageShell title="Dashboard">
      {/* Stats Cards - Improved spacing and layout */}
      <section className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map(s => (
          <StatCard 
            key={s.title} 
            title={s.title} 
            value={s.value} 
            icon={<img src={s.iconUrl} alt={s.title} className="w-full h-full object-contain" />}
          />
        ))}
      </section>

      {/* Charts Section - Better spacing */}
      <section className="grid gap-6 lg:grid-cols-3">
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6 lg:col-span-2">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">New vs Returning Customer Sales</h3>
            <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
              View Details
            </button>
          </div>
          <AreaLine labels={days} datasets={areaSets} />
        </div>
        
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Sessions by Device Type</h3>
            <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
              View Details
            </button>
          </div>
          <Donut labels={['Mobile', 'Desktop', 'Tablet']} dataPoints={[21718, 7319, 182]} centerText="21.8K" />
        </div>
      </section>

      {/* Bottom Charts Section */}
      <section className="grid gap-6 lg:grid-cols-2">
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Conversion Rate Breakdown</h3>
            <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
              View Details
            </button>
          </div>
          <Bar labels={['Sessions', 'Add to cart', 'Reached Checkout', 'Completed Checkout']} dataPoints={[10000, 4200, 3050, 2450]} />
        </div>
        
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Orders and Returns by Product</h3>
            <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
              View Details
            </button>
          </div>
          <Bar labels={['Dahi Korma', 'Bucket Chicken Biryani', 'Chicken 65', 'Chicken Fry', 'Biryani Combo']} dataPoints={[120, 180, 140, 200, 220]} />
        </div>
      </section>
    </PageShell>
  )
}