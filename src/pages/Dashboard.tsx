import PageShell from '../components/PageShell'
import StatCard from '../components/StatCard'
import AreaLine from '../components/charts/AreaLine'
import totalOrderImg from "../assets/total-orders.png"
import preparingImg from "../assets/preparing.png"
import deliveryImg from "../assets/delivered.png";
import cancelledImg from "../assets/cancelled.png";
import totalRevenueImg from "../assets/total-revenue.png";
import orderAbandonImg from "../assets/order-abundant.png";
import activeCouponsIMg from "../assets/active-coupons.png";
import newUsersImg from "../assets/delivered.png";


export default function Dashboard() {
  const stats = [
    { title: 'Total Orders', value: 312, iconUrl: totalOrderImg },
    { title: 'Preparing', value: 87, iconUrl: preparingImg },
    { title: 'Delivered', value: 39, iconUrl: deliveryImg },
    { title: 'Cancelled', value: 10, iconUrl: cancelledImg },
    { title: 'Total Revenue', value: '₹47,890', iconUrl: totalRevenueImg },
    { title: 'Order Abundant', value: 32, iconUrl: orderAbandonImg },
    { title: 'Active Coupons', value: 12, iconUrl: activeCouponsIMg },
    { title: 'New Users', value: 145, iconUrl: newUsersImg },
  ]

  // Chart data for Revenue and Orders
  const weekDays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
  const revenueData = [41000, 43000, 45000, 49000, 52000, 54000, 56000]
  const ordersData = [45, 50, 65, 80, 100, 120, 150]

  // Top performing dishes data
  const topDishes = [
    { name: 'Chicken 65 Biryani', orders: 239 },
    { name: 'Chicken 65 Biryani', orders: 212 },
    { name: 'Chicken 65 Biryani', orders: 184 },
    { name: 'Chicken 65 Biryani', orders: 166 },
    { name: 'Chicken 65 Biryani', orders: 157 },
  ]

  // City-wise summary data
  const citySummary = [
    {
      city: 'Bengaluru',
      orders: 220,
      revenue: '₹42,300',
      avgOrder: '₹192',
      newUsers: 35,
      activeCoupons: 6
    },
    {
      city: 'Chennai',
      orders: 200,
      revenue: '₹40,150',
      avgOrder: '₹201',
      newUsers: 78,
      activeCoupons: 8
    }
  ]

  return (
    <PageShell
      title="Dashboard"
      actions={
        <div className="flex items-center gap-2">
          <select className="rounded-xl border-gray-200 px-3 py-2 text-sm">
            <option>This month</option>
          </select>
          <select className="rounded-xl border-gray-200 px-3 py-2 text-sm">
            <option>Bengaluru</option>
          </select>
        </div>
      }
    >
      {/* Stats Cards - 2 rows of 4 cards each */}
      <section className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map(s => (
          <StatCard
            key={s.title}
            title={s.title}
            value={s.value}
            icon={<img src={s.iconUrl} alt={s.title} className="w-full h-full object-contain" />}
          />
        ))}
      </section>

      {/* Revenue and Orders Charts */}
      <section className="grid gap-6 lg:grid-cols-2">
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Revenue</h3>
            <span className="text-sm text-gray-500">This week</span>
          </div>
          <AreaLine
            labels={weekDays}
            datasets={[{
              label: 'Revenue',
              data: revenueData,
              borderColor: '#f59e0b',
              backgroundColor: 'rgba(245, 158, 11, 0.1)'
            }]}
            height={200}
          />
        </div>

        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Orders</h3>
            <span className="text-sm text-gray-500">This week</span>
          </div>
          <AreaLine
            labels={weekDays}
            datasets={[{
              label: 'Orders',
              data: ordersData,
              borderColor: '#10b981',
              backgroundColor: 'rgba(16, 185, 129, 0.1)'
            }]}
            height={200}
          />
        </div>
      </section>

      {/* Top 5 Performing Dishes */}
      <section className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Top 5 Performing Dishes</h3>
          <span className="text-sm text-gray-500">This week</span>
        </div>
        <div className="space-y-4">
          {topDishes.map((dish, index) => (
            <div key={index} className="flex items-center gap-4">
              <div className="w-8 h-8 rounded-full bg-orange-100 flex items-center justify-center flex-shrink-0">
                <div className="w-6 h-6 rounded-full bg-orange-200"></div>
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium text-gray-900">{dish.name}</span>
                  <span className="text-sm font-semibold text-gray-900">{dish.orders}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-red-500 h-2 rounded-full"
                    style={{ width: `${(dish.orders / 239) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* City-wise Summary Table */}
      <section className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">City-wise Summary</h3>
            <span className="text-sm text-gray-500">This week</span>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm">
            <thead className="bg-gray-50 text-gray-500">
              <tr>
                <th className="text-left px-6 py-3 font-medium">City</th>
                <th className="text-left px-6 py-3 font-medium">Order</th>
                <th className="text-left px-6 py-3 font-medium">Revenue</th>
                <th className="text-left px-6 py-3 font-medium">Avg Order</th>
                <th className="text-left px-6 py-3 font-medium">New Users</th>
                <th className="text-left px-6 py-3 font-medium">Active Coupons</th>
              </tr>
            </thead>
            <tbody>
              {citySummary.map((city, index) => (
                <tr key={index} className="border-t border-gray-200">
                  <td className="px-6 py-4 font-medium text-gray-900">{city.city}</td>
                  <td className="px-6 py-4 text-gray-900">{city.orders}</td>
                  <td className="px-6 py-4 text-gray-900">{city.revenue}</td>
                  <td className="px-6 py-4 text-gray-900">{city.avgOrder}</td>
                  <td className="px-6 py-4 text-gray-900">{city.newUsers}</td>
                  <td className="px-6 py-4 text-gray-900">{city.activeCoupons}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </section>
    </PageShell>
  )
}