import PageShell from '../components/PageShell'
import StatCard from '../components/StatCard'
import AreaLine from '../components/charts/AreaLine'
import totalOrderImg from "../assets/total-orders.png"
import preparingImg from "../assets/preparing.png"
import deliveryImg from "../assets/delivered.png";
import cancelledImg from "../assets/cancelled.png";
import totalRevenueImg from "../assets/total-revenue.png";
import orderAbandonImg from "../assets/order-abundant.png";
import activeCouponsIMg from "../assets/active-coupons.png";
import newUsersImg from "../assets/delivered.png";


export default function Dashboard() {
  const stats = [
    { title: 'Total Orders', value: 312, iconUrl: totalOrderImg },
    { title: 'Preparing', value: 87, iconUrl: preparingImg },
    { title: 'Delivered', value: 39, iconUrl: deliveryImg },
    { title: 'Cancelled', value: 10, iconUrl: cancelledImg },
    { title: 'Total Revenue', value: '₹47,890', iconUrl: totalRevenueImg },
    { title: 'Order Abundant', value: 32, iconUrl: orderAbandonImg },
    { title: 'Active Coupons', value: 12, iconUrl: activeCouponsIMg },
    { title: 'New Users', value: 145, iconUrl: newUsersImg },
  ]

  // Chart data for New vs Returning Customer Sales
  const customerSalesData = {
    labels: ['Jan 1', 'Jan 2', 'Jan 3', 'Jan 4', 'Jan 5', 'Jan 6', 'Jan 7', 'Jan 8', 'Jan 9', 'Jan 10'],
    datasets: [
      {
        label: 'Returning',
        data: [200, 180, 220, 240, 260, 280, 300, 320, 340, 360],
        borderColor: '#ef4444',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        fill: true
      },
      {
        label: 'New',
        data: [150, 160, 170, 180, 190, 200, 210, 220, 230, 240],
        borderColor: '#22c55e',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        fill: true
      }
    ]
  }



  // Returning customer rate over time
  const returningCustomerData = {
    labels: ['Jan 1', 'Jan 2', 'Jan 3', 'Jan 4', 'Jan 5', 'Jan 6', 'Jan 7', 'Jan 8', 'Jan 9', 'Jan 10'],
    datasets: [{
      label: 'Returning Customer Rate',
      data: [65, 68, 70, 72, 75, 73, 76, 78, 80, 82],
      borderColor: '#ef4444',
      backgroundColor: 'rgba(239, 68, 68, 0.1)',
      fill: true
    }]
  }

  // Checkout conversion rate over time
  const checkoutConversionData = {
    labels: ['Jan 1', 'Jan 2', 'Jan 3', 'Jan 4', 'Jan 5', 'Jan 6', 'Jan 7', 'Jan 8', 'Jan 9', 'Jan 10'],
    datasets: [{
      label: 'Checkout Conversion Rate',
      data: [45, 48, 50, 52, 55, 53, 56, 58, 60, 62],
      borderColor: '#ef4444',
      backgroundColor: 'rgba(239, 68, 68, 0.1)',
      fill: true
    }]
  }

  // Total Sales Over Time table data
  const salesOverTime = [
    { date: 'Jun 1-30, 2026', netSales: '₹4,208,928.00', shippingCharges: '₹900.00', totalFees: '₹0.00', taxes: '₹35', totalSales: '₹1,00,000.00' },
    { date: 'Jun 1, 2025', netSales: '₹4,208,928.00', shippingCharges: '₹900.00', totalFees: '₹0.00', taxes: '₹35', totalSales: '₹1,00,000.00' },
    { date: 'Jun 2, 2025', netSales: '₹4,208,928.00', shippingCharges: '₹900.00', totalFees: '₹0.00', taxes: '₹35', totalSales: '₹1,00,000.00' },
    { date: 'Jun 3, 2025', netSales: '₹4,208,928.00', shippingCharges: '₹900.00', totalFees: '₹0.00', taxes: '₹35', totalSales: '₹1,00,000.00' },
    { date: 'Jun 4, 2025', netSales: '₹4,208,928.00', shippingCharges: '₹900.00', totalFees: '₹0.00', taxes: '₹35', totalSales: '₹1,00,000.00' },
    { date: 'Jun 5, 2025', netSales: '₹4,208,928.00', shippingCharges: '₹900.00', totalFees: '₹0.00', taxes: '₹35', totalSales: '₹1,00,000.00' },
  ]

  return (
    <PageShell
      title="Dashboard"
      showSearch={true}
      showTimeFilter={true}
      showLocationFilter={true}
    >
      {/* Stats Cards - 2 rows of 4 cards each */}
      <section className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map(s => (
          <StatCard
            key={s.title}
            title={s.title}
            value={s.value}
            icon={<img src={s.iconUrl} alt={s.title} className="w-full h-full object-contain" />}
          />
        ))}
      </section>

      {/* Charts Section - First Row */}
      <section className="grid gap-6 lg:grid-cols-2">
        {/* New vs Returning Customer Sales */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">New vs Returning Customer Sales</h3>
            <button className="text-sm text-blue-600 hover:text-blue-700">View details</button>
          </div>
          <AreaLine
            labels={customerSalesData.labels}
            datasets={customerSalesData.datasets}
            height={200}
          />
        </div>

        {/* Sessions by Device Type - Donut Chart */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Sessions by Device Type</h3>
            <button className="text-sm text-blue-600 hover:text-blue-700">View details</button>
          </div>
          <div className="flex items-center justify-center h-48">
            <div className="relative">
              <div className="w-32 h-32 rounded-full border-8 border-blue-500 border-r-red-500 border-b-yellow-500"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-2xl font-bold">21.8K</div>
                  <div className="text-sm text-gray-500">Total</div>
                </div>
              </div>
            </div>
            <div className="ml-8 space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                <span className="text-sm">Desktop 45.8%</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span className="text-sm">Mobile 32.1%</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span className="text-sm">Tablet 22.1%</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Charts Section - Second Row */}
      <section className="grid gap-6 lg:grid-cols-2">
        {/* Conversion Rate Breakdown */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Conversion Rate Breakdown</h3>
            <button className="text-sm text-blue-600 hover:text-blue-700">View details</button>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Landing Page</span>
              <span className="text-sm font-semibold">85%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-8">
              <div className="bg-red-500 h-8 rounded-full flex items-center justify-end pr-2" style={{ width: '85%' }}>
                <span className="text-white text-xs font-medium">85%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Product Page</span>
              <span className="text-sm font-semibold">65%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-8">
              <div className="bg-green-500 h-8 rounded-full flex items-center justify-end pr-2" style={{ width: '65%' }}>
                <span className="text-white text-xs font-medium">65%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Cart Page</span>
              <span className="text-sm font-semibold">45%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-8">
              <div className="bg-yellow-500 h-8 rounded-full flex items-center justify-end pr-2" style={{ width: '45%' }}>
                <span className="text-white text-xs font-medium">45%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Checkout Page</span>
              <span className="text-sm font-semibold">25%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-8">
              <div className="bg-blue-500 h-8 rounded-full flex items-center justify-end pr-2" style={{ width: '25%' }}>
                <span className="text-white text-xs font-medium">25%</span>
              </div>
            </div>
          </div>
        </div>

        {/* Orders and Returns by Product */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Orders and Returns by Product</h3>
            <button className="text-sm text-blue-600 hover:text-blue-700">View details</button>
          </div>
          <div className="space-y-4">
            {['Grilled Salmon', 'Roasted Chicken', 'Chicken Stir Fry', 'Chicken Biryani', 'Chicken Burger'].map((product, index) => {
              const orders = [450, 380, 320, 280, 240][index]
              const returns = [20, 15, 12, 10, 8][index]
              const maxOrders = 450
              return (
                <div key={product} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">{product}</span>
                    <div className="flex gap-4">
                      <span className="text-sm font-semibold text-red-600">{orders}</span>
                      <span className="text-sm font-semibold text-yellow-600">{returns}</span>
                    </div>
                  </div>
                  <div className="flex gap-1">
                    <div className="flex-1 bg-gray-200 rounded-full h-6">
                      <div
                        className="bg-red-500 h-6 rounded-full"
                        style={{ width: `${(orders / maxOrders) * 100}%` }}
                      ></div>
                    </div>
                    <div className="w-16 bg-gray-200 rounded-full h-6">
                      <div
                        className="bg-yellow-500 h-6 rounded-full"
                        style={{ width: `${(returns / 20) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Charts Section - Third Row */}
      <section className="grid gap-6 lg:grid-cols-2">
        {/* Returning Customer Rate Over Time */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Returning Customer Rate Over Time</h3>
            <button className="text-sm text-blue-600 hover:text-blue-700">View details</button>
          </div>
          <AreaLine
            labels={returningCustomerData.labels}
            datasets={returningCustomerData.datasets}
            height={200}
          />
        </div>

        {/* Checkout Conversion Rate Over Time */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Checkout Conversion Rate Over Time</h3>
            <button className="text-sm text-blue-600 hover:text-blue-700">View details</button>
          </div>
          <AreaLine
            labels={checkoutConversionData.labels}
            datasets={checkoutConversionData.datasets}
            height={200}
          />
        </div>
      </section>

      {/* Total Sales Over Time Table */}
      <section className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Total Sales Over Time</h3>
            <button className="text-sm text-blue-600 hover:text-blue-700">View details</button>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm">
            <thead className="bg-gray-50 text-gray-500">
              <tr>
                <th className="text-left px-6 py-3 font-medium">Day</th>
                <th className="text-left px-6 py-3 font-medium">Net Sales</th>
                <th className="text-left px-6 py-3 font-medium">Shipping Charges</th>
                <th className="text-left px-6 py-3 font-medium">Total Fees</th>
                <th className="text-left px-6 py-3 font-medium">Taxes</th>
                <th className="text-left px-6 py-3 font-medium">Total Sales</th>
              </tr>
            </thead>
            <tbody>
              {salesOverTime.map((row, index) => (
                <tr key={index} className="border-t border-gray-200">
                  <td className="px-6 py-4 font-medium text-gray-900">{row.date}</td>
                  <td className="px-6 py-4 text-gray-900">{row.netSales}</td>
                  <td className="px-6 py-4 text-gray-900">{row.shippingCharges}</td>
                  <td className="px-6 py-4 text-gray-900">{row.totalFees}</td>
                  <td className="px-6 py-4 text-gray-900">{row.taxes}</td>
                  <td className="px-6 py-4 text-gray-900 font-semibold">{row.totalSales}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </section>
    </PageShell>
  )
}