import { useMemo, useState } from 'react'
import PageShell from '../components/PageShell'
import StatCard from '../components/StatCard'
import SubscriptionPill from '../components/SubscriptionPill'
import Badge from '../components/Badge'

type Row = {
  id: string
  name: string
  email?: string
  subscribed: boolean
  location: string
  orders: number
  amountSpent: number
  isNew?: boolean
}

export default function Customers() {
  const [q, setQ] = useState('')
  const [loc, setLoc] = useState<'All' | 'Bengaluru' | 'Chennai'>('All')

  // ---- demo data; swap with Supabase later ----
  const rows: Row[] = useMemo(
    () => [
      { id: '1', name: '<PERSON><PERSON><PERSON><PERSON>', subscribed: true,  location: 'Chennai TN, India',         orders: 0, amountSpent: 1133.06, isNew: true },
      { id: '2', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',  subscribed: false, location: 'Bengaluru KA, India',       orders: 1, amountSpent: 1133.06 },
      { id: '3', name: '<EMAIL>', subscribed: true, location: 'Bengaluru KA, India', orders: 1, amountSpent: 1133.06 },
      { id: '4', name: 'Cha<PERSON><PERSON> Kumar<PERSON>', subscribed: true, location: 'Bengaluru KA, India',       orders: 1, amountSpent: 1133.06 },
      { id: '5', name: '<EMAIL>', subscribed: true, location: 'Bengaluru KA, India', orders: 1, amountSpent: 1133.06 },
      { id: '6', name: 'Deepak Kumar', subscribed: false, location: 'Bengaluru KA, India',          orders: 1, amountSpent: 1133.06 },
      { id: '7', name: 'Deepak Kumar', subscribed: false, location: 'Bengaluru KA, India',          orders: 1, amountSpent: 1133.06 },
      // add more mock rows if you want
    ],
    []
  )

  const filtered = rows.filter(r => {
    const inLoc = loc === 'All' ? true : r.location.toLowerCase().includes(loc.toLowerCase())
    const inQ   = !q ? true : (r.name + ' ' + (r.email ?? '')).toLowerCase().includes(q.toLowerCase())
    return inLoc && inQ
  })

  // pagination
  const [page, setPage] = useState(1)
  const pageSize = 8
  const pages = Math.max(1, Math.ceil(filtered.length / pageSize))
  const pageRows = filtered.slice((page - 1) * pageSize, page * pageSize)

  const fmtINR = (n: number) => n.toLocaleString('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 2 })

  // CSV
  const downloadCsv = () => {
    if (!filtered.length) return
    const headers = ['name','email','subscribed','location','orders','amount_spent']
    const csv = [headers.join(','), ...filtered.map(r =>
      [r.name, r.email ?? '', r.subscribed ? 'yes' : 'no', r.location, r.orders, r.amountSpent].join(',')
    )].join('\n')
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob); const a = document.createElement('a')
    a.href = url; a.download = 'customers.csv'; a.click(); URL.revokeObjectURL(url)
  }

  return (
    <PageShell
      title="Customers"
      actions={
        <div className="flex items-center gap-2">
          <select className="rounded-xl border-gray-200"><option>This month</option></select>
          <select className="rounded-xl border-gray-200"><option>Bengaluru</option></select>
        </div>
      }
    >
      {/* metrics row */}
      <section className="grid gap-4 sm:grid-cols-2 xl:grid-cols-4">
        <StatCard title="Total Customers" value={1706} />
        <StatCard title="New Users" value={145} />
        <StatCard title="From Chennai" value={964} />
        <StatCard title="From Bengaluru" value={742} />
      </section>

      {/* list header */}
      <div className="mt-6 flex items-center justify-between">
        <h2 className="text-lg font-semibold">Customers List</h2>
        <div className="flex items-center gap-2">
          <div className="relative">
            <input
              value={q}
              onChange={e => { setQ(e.target.value); setPage(1) }}
              placeholder="Search"
              className="rounded-xl border-gray-200 pl-9"
            />
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">🔍</span>
          </div>
          <button onClick={downloadCsv} className="rounded-xl bg-emerald-100 text-emerald-700 px-3.5 py-2 text-sm">
            ⤓ Download CSV
          </button>
          <select
            value={loc}
            onChange={e => { setLoc(e.target.value as any); setPage(1) }}
            className="rounded-xl border-gray-200 text-sm"
          >
            <option>All</option>
            <option>Bengaluru</option>
            <option>Chennai</option>
          </select>
        </div>
      </div>

      {/* table */}
      <div className="mt-3 bg-white rounded-2xl border shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm">
            <thead className="bg-white text-gray-500 border-b">
              <tr>
                <th className="text-left px-4 py-3">Customer name</th>
                <th className="text-left px-4 py-3">Email subscription</th>
                <th className="text-left px-4 py-3">Location</th>
                <th className="text-left px-4 py-3">Orders</th>
                <th className="text-left px-4 py-3">Amount spent</th>
              </tr>
            </thead>
            <tbody>
              {pageRows.map(r => (
                <tr key={r.id} className="border-t">
                  <td className="px-4 py-3">
                    <div className="flex items-center gap-2">
                      {r.isNew && <Badge tone="warning">NEW</Badge>}
                      <span className="font-medium">{r.name}</span>
                    </div>
                    {r.email && <div className="text-xs text-gray-500">{r.email}</div>}
                  </td>
                  <td className="px-4 py-3"><SubscriptionPill subscribed={r.subscribed} /></td>
                  <td className="px-4 py-3">{r.location}</td>
                  <td className="px-4 py-3">{r.orders}</td>
                  <td className="px-4 py-3">{fmtINR(r.amountSpent)}</td>
                </tr>
              ))}
              {!pageRows.length && (
                <tr><td className="px-4 py-6 text-center text-gray-500" colSpan={5}>No customers</td></tr>
              )}
            </tbody>
          </table>
        </div>

        {/* pagination */}
        <div className="p-3 flex items-center justify-center gap-1">
          <button
            className="px-2 py-1 rounded-lg bg-gray-100"
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
          >←</button>
          {Array.from({ length: pages }).map((_, i) => (
            <button
              key={i}
              onClick={() => setPage(i + 1)}
              className={`size-8 rounded-lg ${page === i + 1 ? 'bg-gray-900 text-white' : 'bg-gray-100'}`}
            >{i + 1}</button>
          ))}
          <button
            className="px-2 py-1 rounded-lg bg-gray-100"
            onClick={() => setPage(p => Math.min(pages, p + 1))}
            disabled={page >= pages}
          >→</button>
        </div>
      </div>
    </PageShell>
  )
}
