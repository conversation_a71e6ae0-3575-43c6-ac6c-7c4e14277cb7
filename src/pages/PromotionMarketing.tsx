import { useMemo, useState } from 'react'
import PageShell from '../components/PageShell'
import StatCard from '../components/StatCard'
import Modal from '../components/Modal'
import Badge from '../components/Badge'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { supabase } from '../lib/supabaseClient'

type CouponRow = {
  id: string
  code: string
  offer: string
  type: 'Branch-Specific'|'Global'|'Product'
  usage_limit: string
  redeemed: number
  validity: string
  status: 'Active'|'Inactive'|'Expired'
}

const formSchema = z.object({
  location: z.enum(['all','bengaluru','chennai']),
  branch: z.string().min(1, 'Select branch'),
  code: z.string().min(3, 'Enter a code'),
  couponType: z.enum(['Product','Cart']),
  product: z.string().optional(),
  discountType: z.enum(['Flat','Percent']),
  discountValue: z.string().min(1),
  usageLimit: z.string().min(1),
  minOrder: z.string().optional(),
  maxDiscount: z.string().optional(),
  description: z.string().optional(),
  userType: z.enum(['All users','New user only']),
  startDate: z.string().min(1),
  endDate: z.string().min(1),
})

type FormVals = z.infer<typeof formSchema>

export default function PromotionMarketing() {
  const [open, setOpen] = useState(false)
  const [statusFilter, setStatusFilter] = useState<'All'|'Active'|'Inactive'|'Expired'>('All')

  const rows: CouponRow[] = useMemo(() => ([
    { id:'1', code:'NEWUSER50', offer:'Flat ₹50 Off', type:'Branch-Specific', usage_limit:'Unlimited', redeemed:214, validity:'1 Apr – 30 Apr', status:'Active' },
    { id:'2', code:'FREEJAMUN8', offer:'Free 8 Gulab Jamuns', type:'Branch-Specific', usage_limit:'Unlimited', redeemed:214, validity:'1 Apr – 30 Apr', status:'Active' },
    { id:'3', code:'BIRYANI50', offer:'Flat ₹50 Off', type:'Branch-Specific', usage_limit:'Unlimited', redeemed:214, validity:'1 Apr – 30 Apr', status:'Inactive' },
    { id:'4', code:'OLDAPRIL', offer:'Free 8 Gulab Jamuns', type:'Branch-Specific', usage_limit:'Unlimited', redeemed:214, validity:'1 Apr – 30 Apr', status:'Expired' },
  ]), [])

  const filtered = rows.filter(r => statusFilter==='All' ? true : r.status===statusFilter)

  return (
    <PageShell
      title="Promotions & Marketing"
      showSearch={true}
      showTimeFilter={true}
      showLocationFilter={true}
    >
      {/* Top metrics - 2 rows of 4 cards each */}
      <section className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Active Coupons"
          value={12}
          icon={
            <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
              <path d="M12.79 21L3 11.21v2c0 .45.54.67.85.35l.79-.79 1.04 1.04c.78.78 2.05.78 2.83 0l1.04-1.04 1.04 1.04c.78.78 2.05.78 2.83 0l1.04-1.04 1.04 1.04c.78.78 2.05.78 2.83 0L12.79 21z"/>
            </svg>
          }
        />
        <StatCard
          title="Redemptions Today"
          value={320}
          icon={
            <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
              <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-5H19V4h-3V2H8v2H5v2h-.5C3.67 6 3 6.67 3 7.5S3.67 9 4.5 9H5v10c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V9h.5c.83 0 1.5-.67 1.5-1.5S20.33 6 19.5 6z"/>
            </svg>
          }
        />
        <StatCard
          title="Discount Given Today"
          value="₹7,450"
          icon={
            <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          }
        />
        <StatCard
          title="Top Performer"
          value="BIRYANI50"
          icon={
            <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          }
        />
        <StatCard
          title="Redemption Rate"
          value="18.5%"
          icon={
            <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
              <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
            </svg>
          }
        />
        <StatCard
          title="New Users"
          value={145}
          icon={
            <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
              <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
            </svg>
          }
        />
        <StatCard
          title="Expiring Soon"
          value={3}
          icon={
            <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
              <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
              <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
            </svg>
          }
        />
        <StatCard
          title="Popular Type"
          value="Flat ₹50 Off"
          icon={
            <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
              <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
            </svg>
          }
        />
      </section>

      {/* List header + actions */}
      <div className="flex items-center justify-between mt-6">
        <h2 className="text-lg font-semibold">Coupons List</h2>
        <div className="flex items-center gap-2">
          <button onClick={() => setOpen(true)} className="rounded-lg bg-red-500 hover:bg-red-600 text-white px-4 py-2 text-sm font-medium transition-colors">
            + Create New
          </button>
          <button onClick={() => downloadCsv(filtered)} className="rounded-lg bg-green-500 hover:bg-green-600 text-white px-4 py-2 text-sm font-medium transition-colors">
            ✓ Download CSV
          </button>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Status:</span>
            <select
              value={statusFilter}
              onChange={e=>setStatusFilter(e.target.value as 'All'|'Active'|'Inactive'|'Expired')}
              className="rounded-lg border-gray-200 text-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
            >
              <option>All</option>
              <option>Active</option>
              <option>Inactive</option>
              <option>Expired</option>
            </select>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="mt-3 bg-white rounded-2xl border shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm">
            <thead className="bg-white text-gray-500 border-b">
              <tr>
                <th className="text-left px-4 py-3">Coupon Code</th>
                <th className="text-left px-4 py-3">Offer</th>
                <th className="text-left px-4 py-3">Type</th>
                <th className="text-left px-4 py-3">Usage limit</th>
                <th className="text-left px-4 py-3">Redeemed</th>
                <th className="text-left px-4 py-3">Validity</th>
                <th className="text-left px-4 py-3">Status</th>
                <th className="text-left px-4 py-3">Action</th>
              </tr>
            </thead>
            <tbody>
              {filtered.map(r => (
                <tr key={r.id} className="border-t">
                  <td className="px-4 py-3">
                    <div className="flex items-center gap-2">
                      {r.code.startsWith('NEW') && <Badge tone="warning">NEW</Badge>}
                      <span className="font-medium">{r.code}</span>
                    </div>
                  </td>
                  <td className="px-4 py-3">{r.offer}</td>
                  <td className="px-4 py-3">{r.type}</td>
                  <td className="px-4 py-3">{r.usage_limit}</td>
                  <td className="px-4 py-3">{r.redeemed}</td>
                  <td className="px-4 py-3">{r.validity}</td>
                  <td className="px-4 py-3">
                    {r.status === 'Active'   && <Badge tone="success">Active</Badge>}
                    {r.status === 'Inactive' && <Badge tone="danger">Inactive</Badge>}
                    {r.status === 'Expired'  && <Badge tone="muted">Expired</Badge>}
                  </td>
                  <td className="px-4 py-3">
                    <button className="w-8 h-8 rounded-full bg-gray-600 text-white hover:bg-gray-700 flex items-center justify-center text-sm font-bold transition-colors">
                      V
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* pagination */}
        <div className="p-4 flex items-center justify-center gap-2">
          <button className="px-3 py-1 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 transition-colors">
            ←
          </button>
          {Array.from({length: 10}).map((_,i)=>(
            <button
              key={i}
              className={`w-8 h-8 rounded-lg transition-colors ${
                i===0
                  ? 'bg-gray-900 text-white'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
              }`}
            >
              {i+1}
            </button>
          ))}
          <button className="px-3 py-1 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 transition-colors">
            →
          </button>
        </div>
      </div>

      {/* CREATE NEW MODAL */}
      <CreateCouponModal open={open} onClose={()=>setOpen(false)} />
    </PageShell>
  )
}

/* ----------------- Modal with form ----------------- */
function CreateCouponModal({ open, onClose }: { open: boolean; onClose: () => void }) {
  const { register, handleSubmit, formState: { errors, isSubmitting }, reset } = useForm<FormVals>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      location: 'all', branch: 'Whitefield', code: '', couponType: 'Product',
      product: '', discountType: 'Flat', discountValue: '', usageLimit: '100',
      minOrder: '', maxDiscount: '', description: '', userType: 'All users',
      startDate: '2025-04-01', endDate: '2025-04-10',
    }
  })

  const submit = async (vals: FormVals) => {
    // TODO: replace with your table/columns
    try {
      // Example insert (adjust table + columns)
      await supabase.from('coupons').insert({
        code: vals.code,
        branch: vals.branch,
        coupon_type: vals.couponType,
        discount_type: vals.discountType,
        discount_value: vals.discountValue,
        usage_limit: vals.usageLimit,
        min_order: vals.minOrder,
        max_discount: vals.maxDiscount,
        description: vals.description,
        user_type: vals.userType,
        start_date: vals.startDate,
        end_date: vals.endDate,
        location: vals.location,
        product: vals.product,
        status: 'Active'
      })
      reset()
      onClose()
    } catch (e) {
      console.error(e)
      alert('Failed to create coupon (configure Supabase table).')
    }
  }

  return (
    <Modal open={open} onClose={onClose} title="Create New Coupon" widthClass="max-w-xl">
      <form className="space-y-4" onSubmit={handleSubmit(submit)}>
        {/* Location */}
        <div>
          <label className="block text-sm font-medium mb-1">Applicable location *</label>
          <div className="flex gap-4 text-sm">
            <label className="flex items-center gap-2"><input type="radio" value="all" {...register('location')} />All</label>
            <label className="flex items-center gap-2"><input type="radio" value="bengaluru" {...register('location')} />Bengaluru</label>
            <label className="flex items-center gap-2"><input type="radio" value="chennai" {...register('location')} />Chennai</label>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <Field label="Select branch *">
            <select className="rounded-xl" {...register('branch')}>
              <option>Whitefield</option><option>Indiranagar</option><option>HSR</option>
            </select>
            <Error msg={errors.branch?.message} />
          </Field>

          <Field label="Coupon code *">
            <input className="rounded-xl" placeholder="NEWUSER50" {...register('code')} />
            <Error msg={errors.code?.message} />
          </Field>

          <Field label="Coupon type *">
            <select className="rounded-xl" {...register('couponType')}>
              <option>Product</option><option>Cart</option>
            </select>
          </Field>

          <Field label="Search product">
            <div className="relative">
              <input className="rounded-xl pl-8" placeholder="Chicken Biryani" {...register('product')} />
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">🔍</span>
            </div>
          </Field>

          <Field label="Discount type *">
            <select className="rounded-xl" {...register('discountType')}>
              <option>Flat</option><option>Percent</option>
            </select>
          </Field>

          <Field label="Discount value *">
            <input className="rounded-xl" placeholder="₹100 or 10" {...register('discountValue')} />
            <Error msg={errors.discountValue?.message} />
          </Field>

          <Field label="Usage limit *">
            <input className="rounded-xl" placeholder="100" {...register('usageLimit')} />
            <Error msg={errors.usageLimit?.message} />
          </Field>

          <Field label="Minimum order amount">
            <input className="rounded-xl" placeholder="₹100" {...register('minOrder')} />
          </Field>

          <Field label="Maximum discount amount">
            <input className="rounded-xl" placeholder="₹200" {...register('maxDiscount')} />
          </Field>

          <Field className="sm:col-span-2" label="Description">
            <textarea rows={3} className="rounded-xl" placeholder="₹200 off on all orders" {...register('description')} />
          </Field>

          <Field label="Type">
            <select className="rounded-xl" {...register('userType')}>
              <option>All users</option><option>New user only</option>
            </select>
          </Field>

          <Field label="Start date *">
            <input type="date" className="rounded-xl" {...register('startDate')} />
            <Error msg={errors.startDate?.message} />
          </Field>

          <Field label="End date *">
            <input type="date" className="rounded-xl" {...register('endDate')} />
            <Error msg={errors.endDate?.message} />
          </Field>
        </div>

        <div className="pt-2">
          <button disabled={isSubmitting} className="w-full rounded-2xl bg-rose-600 text-white py-3">
            {isSubmitting ? 'Creating…' : 'Create Coupon'}
          </button>
        </div>
      </form>
    </Modal>
  )
}

function Field({ label, children, className='' }: { label: string; children: React.ReactNode; className?: string }) {
  return (
    <div className={className}>
      <label className="block text-sm font-medium mb-1">{label}</label>
      {children}
    </div>
  )
}
function Error({ msg }: { msg?: string }) {
  if (!msg) return null
  return <p className="text-xs text-red-600 mt-1">{msg}</p>
}

/* ---------------- CSV helper ---------------- */
function downloadCsv(rows: CouponRow[]) {
  const headers = ['code','offer','type','usage_limit','redeemed','validity','status']
  const csv = [headers.join(','), ...rows.map(r => headers.map(h => (r as any)[h]).join(','))].join('\n')
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url; a.download = 'coupons.csv'; a.click()
  URL.revokeObjectURL(url)
}
