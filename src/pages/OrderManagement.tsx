import { useMemo, useState } from 'react'
import PageShell from '../components/PageShell'
import StatCard from '../components/StatCard'
import StatusPill from '../components/StatusPill'
import Badge from '../components/Badge'

type LiveOrder = {
  id: string
  isNew?: boolean
  customer: string
  phone: string
  itemTitle: string
  itemSub: string
  branchCity: string
  branchState: string
  amount: number
  payment: 'Online' | 'Card' | 'Cash'
  status: Parameters<typeof StatusPill>[0]['status']
}

type AbandonedRow = {
  checkoutId: string
  date: string
  time: string
  customer: string
  phone: string
  region: string
  emailStatus: 'Sent' | 'Not Sent'
  recovered: 'Recovered' | 'Not Recovered'
  amount: number
}

type HistoryRow = {
  orderId: string
  date: string
  time: string
  customer: string
  phone: string
  itemTitle: string
  itemSub: string
  branchCity: string
  branchState: string
  status: Parameters<typeof StatusPill>[0]['status']
  amount: number
  payment: 'Online' | 'Card' | 'Cash'
}

export default function OrderManagement() {
  const [tab, setTab] = useState<'live' | 'abandoned' | 'history'>('live')
  const [statusFilter, setStatusFilter] =
    useState<'All' | 'Preparing' | 'Ready to Pickup' | 'Out for Delivery' | 'Delivered' | 'Cancelled' | 'Failed'>('All')

  // -------- demo data; swap with Supabase queries later ----------
  const liveOrders: LiveOrder[] = useMemo(
    () =>
      Array.from({ length: 10 }).map((_, i) => ({
        id: `#73810${9 - (i % 5)}`,
        isNew: i < 2,
        customer: 'Abhishek Tripathi',
        phone: '+91-9090909090',
        itemTitle: 'Chicken 65 Biryani +4',
        itemSub: 'Chicken Kabab Fry, Boneless Chicken, …',
        branchCity: 'Electronic City',
        branchState: 'Bengaluru, Karnataka',
        amount: 999,
        payment: 'Online',
        status: (['Out for Delivery', 'Ready to Pickup', 'Preparing', 'Delivered', 'Ready to Pickup'] as const)[i % 5]
      })),
    []
  )

  const abandoned: AbandonedRow[] = useMemo(
    () =>
      Array.from({ length: 10 }).map((_, i) => ({
        checkoutId: `#73810992817${400 + i}`,
        date: '13 Sep, 2025',
        time: '12:09 PM',
        customer: 'Abhishek Tripathi',
        phone: '+91-9090909090',
        region: 'India',
        emailStatus: (['Sent', 'Not Sent'] as const)[i % 2],
        recovered: (['Not Recovered', 'Recovered'] as const)[(i + 1) % 2],
        amount: 1133.06
      })),
    []
  )

  const history: HistoryRow[] = useMemo(
    () =>
      Array.from({ length: 10 }).map((_, i) => ({
        orderId: `#73810${9 - (i % 5)}`,
        date: '13 Sep, 2025',
        time: '12:09 PM',
        customer: 'Abhishek Tripathi',
        phone: '+91-9090909090',
        itemTitle: 'Chicken 65 Biryani +4',
        itemSub: 'Chicken Kabab Fry, Boneless Chicken, …',
        branchCity: 'Electronic City',
        branchState: 'Bengaluru, Karnataka',
        status: (['Preparing', 'Cancelled', 'Delivered', 'Delivered', 'Failed'] as const)[i % 5],
        amount: 999,
        payment: 'Online'
      })),
    []
  )

  // filters
  const filteredLive = liveOrders.filter(o => (statusFilter === 'All' ? true : o.status === statusFilter))

  // pagination (simple)
  const [page, setPage] = useState(1)
  const pageSize = 6
  const slice = <T,>(rows: T[]) => rows.slice((page - 1) * pageSize, page * pageSize)
  const pages = (len: number) => Math.max(1, Math.ceil(len / pageSize))

  // CSV export
  const downloadCsv = () => {
    const rows =
      tab === 'live'
        ? filteredLive.map(r => ({
            order_id: r.id,
            customer: r.customer,
            phone: r.phone,
            items: r.itemTitle,
            branch: `${r.branchCity} | ${r.branchState}`,
            amount: r.amount,
            payment: r.payment,
            status: r.status
          }))
        : tab === 'abandoned'
        ? abandoned.map(r => ({
            checkout: r.checkoutId,
            date: `${r.date} ${r.time}`,
            customer: r.customer,
            region: r.region,
            email_status: r.emailStatus,
            recovery_status: r.recovered,
            amount: r.amount
          }))
        : history.map(r => ({
            order_id: r.orderId,
            date: `${r.date} ${r.time}`,
            customer: r.customer,
            items: r.itemTitle,
            branch: `${r.branchCity} | ${r.branchState}`,
            status: r.status,
            amount: r.amount,
            payment: r.payment
          }))
    const headers = Object.keys(rows[0] ?? {})
    const csv = [headers.join(','), ...rows.map(r => headers.map(h => (r as any)[h]).join(','))].join('\n')
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${tab}-orders.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  return (
    <PageShell
      title="Order management"
      actions={
        <div className="flex items-center gap-2">
          <select className="rounded-xl border-gray-200"><option>This month</option></select>
          <select className="rounded-xl border-gray-200"><option>Bengaluru</option></select>
        </div>
      }
    >
      {/* metrics */}
      <section className="grid gap-4 sm:grid-cols-2 xl:grid-cols-4">
        <StatCard title="Total Orders Today" value={312} />
        <StatCard title="Preparing" value={87} />
        <StatCard title="Ready for Pickup" value={42} />
        <StatCard title="Out for Delivery" value={39} />
        <StatCard title="Completed" value={134} />
        <StatCard title="Cancelled" value={10} />
        <StatCard title="Avg. Preparation Time" value="18 mins" />
        <StatCard title="Revenue Today" value="₹47,890" />
      </section>

      {/* tab header */}
      <div className="mt-6 flex items-center justify-between gap-3">
        <div className="flex items-center gap-6 text-sm">
          <TabBtn active={tab === 'live'} onClick={() => { setTab('live'); setPage(1) }}>Live Orders</TabBtn>
          <TabBtn active={tab === 'abandoned'} onClick={() => { setTab('abandoned'); setPage(1) }}>Abandoned Checkouts</TabBtn>
          <TabBtn active={tab === 'history'} onClick={() => { setTab('history'); setPage(1) }}>Order History</TabBtn>
        </div>
        <div className="flex items-center gap-2">
          <button onClick={downloadCsv} className="rounded-xl bg-emerald-100 text-emerald-700 px-3.5 py-2 text-sm">
            ⤓ Download CSV
          </button>
          {tab !== 'abandoned' && (
            <select
              value={statusFilter}
              onChange={e => setStatusFilter(e.target.value as any)}
              className="rounded-xl border-gray-200 text-sm"
            >
              <option>All</option>
              <option>Preparing</option>
              <option>Ready to Pickup</option>
              <option>Out for Delivery</option>
              <option>Delivered</option>
              <option>Cancelled</option>
              <option>Failed</option>
            </select>
          )}
        </div>
      </div>

      {/* tables */}
      <div className="bg-white rounded-2xl border shadow-sm mt-3 overflow-hidden">
        {tab === 'live' && <LiveOrdersTable rows={slice(filteredLive)} />}
        {tab === 'abandoned' && <AbandonedTable rows={slice(abandoned)} />}
        {tab === 'history' && <HistoryTable rows={slice(history)} />}

        {/* pagination */}
        <div className="p-3 flex items-center justify-center gap-1">
          <button
            className="px-2 py-1 rounded-lg bg-gray-100"
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            ←
          </button>
          {Array.from({ length: pages(tab === 'live' ? filteredLive.length : tab === 'abandoned' ? abandoned.length : history.length) })
            .map((_, i) => (
              <button
                key={i}
                onClick={() => setPage(i + 1)}
                className={`size-8 rounded-lg ${page === i + 1 ? 'bg-gray-900 text-white' : 'bg-gray-100'}`}
              >
                {i + 1}
              </button>
            ))}
          <button
            className="px-2 py-1 rounded-lg bg-gray-100"
            onClick={() => setPage(p => p + 1)}
            disabled={page >= pages(tab === 'live' ? filteredLive.length : tab === 'abandoned' ? abandoned.length : history.length)}
          >
            →
          </button>
        </div>
      </div>
    </PageShell>
  )
}

/* ---------- sub components (tables) ---------- */

function LiveOrdersTable({ rows }: { rows: LiveOrder[] }) {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full text-sm">
        <thead className="bg-white text-gray-500 border-b">
          <tr>
            <th className="text-left px-4 py-3">Order Id</th>
            <th className="text-left px-4 py-3">Customer name<br /><span className="text-xs text-gray-400">Phone number</span></th>
            <th className="text-left px-4 py-3">Items</th>
            <th className="text-left px-4 py-3">Branch<br /><span className="text-xs text-gray-400">City, State</span></th>
            <th className="text-left px-4 py-3">Amount<br /><span className="text-xs text-gray-400">Payment</span></th>
            <th className="text-left px-4 py-3">Status</th>
          </tr>
        </thead>
        <tbody>
          {rows.map(r => (
            <tr key={r.id} className="border-t">
              <td className="px-4 py-3">
                <div className="flex items-center gap-2">
                  {r.isNew && <Badge tone="warning">NEW</Badge>}
                  <span className="font-medium">{r.id}</span>
                </div>
              </td>
              <td className="px-4 py-3">
                <div className="font-medium truncate max-w-[180px]">{r.customer}</div>
                <div className="text-xs text-gray-500">{r.phone}</div>
              </td>
              <td className="px-4 py-3">
                <div className="flex items-center gap-2">
                  <img src="https://dummyimage.com/48x48/f2f2f2/aaa.png&text=🍛" className="size-10 rounded" />
                  <div>
                    <div className="font-medium">{r.itemTitle}</div>
                    <div className="text-xs text-gray-500 truncate max-w-[260px]">{r.itemSub}</div>
                  </div>
                </div>
              </td>
              <td className="px-4 py-3">
                <div className="font-medium">{r.branchCity}</div>
                <div className="text-xs text-gray-500">{r.branchState}</div>
              </td>
              <td className="px-4 py-3">
                <div className="font-medium">₹{r.amount}</div>
                <div className="text-xs text-gray-500">{r.payment}</div>
              </td>
              <td className="px-4 py-3">
                <StatusPill status={r.status} />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function AbandonedTable({ rows }: { rows: AbandonedRow[] }) {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full text-sm">
        <thead className="bg-white text-gray-500 border-b">
          <tr>
            <th className="text-left px-4 py-3">Checkout</th>
            <th className="text-left px-4 py-3">Date<br /><span className="text-xs text-gray-400">Time</span></th>
            <th className="text-left px-4 py-3">Customer name<br /><span className="text-xs text-gray-400">Phone number</span></th>
            <th className="text-left px-4 py-3">Region</th>
            <th className="text-left px-4 py-3">Email Status</th>
            <th className="text-left px-4 py-3">Recovery Status</th>
            <th className="text-left px-4 py-3">Amount</th>
          </tr>
        </thead>
        <tbody>
          {rows.map(r => (
            <tr key={r.checkoutId} className="border-t">
              <td className="px-4 py-3"><span className="font-medium">{r.checkoutId}</span></td>
              <td className="px-4 py-3">
                <div className="font-medium">{r.date}</div>
                <div className="text-xs text-gray-500">{r.time}</div>
              </td>
              <td className="px-4 py-3">
                <div className="font-medium">{r.customer}</div>
                <div className="text-xs text-gray-500">{r.phone}</div>
              </td>
              <td className="px-4 py-3">{r.region}</td>
              <td className="px-4 py-3">
                {r.emailStatus === 'Sent'
                  ? <span className="inline-flex items-center gap-1 rounded-full bg-emerald-100 text-emerald-700 px-2.5 py-1 text-xs">Sent</span>
                  : <span className="inline-flex items-center gap-1 rounded-full bg-amber-100 text-amber-800 px-2.5 py-1 text-xs">Not Sent ⟳</span>}
              </td>
              <td className="px-4 py-3">
                {r.recovered === 'Recovered'
                  ? <span className="inline-flex items-center gap-1 rounded-full bg-emerald-100 text-emerald-700 px-2.5 py-1 text-xs">Recovered</span>
                  : <span className="inline-flex items-center gap-1 rounded-full bg-amber-100 text-amber-800 px-2.5 py-1 text-xs">Not Recovered</span>}
              </td>
              <td className="px-4 py-3">₹{r.amount.toLocaleString('en-IN')}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function HistoryTable({ rows }: { rows: HistoryRow[] }) {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full text-sm">
        <thead className="bg-white text-gray-500 border-b">
          <tr>
            <th className="text-left px-4 py-3">Order Id</th>
            <th className="text-left px-4 py-3">Date<br /><span className="text-xs text-gray-400">Time</span></th>
            <th className="text-left px-4 py-3">Customer name</th>
            <th className="text-left px-4 py-3">Items</th>
            <th className="text-left px-4 py-3">Branch</th>
            <th className="text-left px-4 py-3">Status</th>
            <th className="text-left px-4 py-3">Amount<br /><span className="text-xs text-gray-400">Payment</span></th>
          </tr>
        </thead>
        <tbody>
          {rows.map(r => (
            <tr key={r.orderId} className="border-t">
              <td className="px-4 py-3"><span className="font-medium">{r.orderId}</span></td>
              <td className="px-4 py-3">
                <div className="font-medium">{r.date}</div>
                <div className="text-xs text-gray-500">{r.time}</div>
              </td>
              <td className="px-4 py-3">
                <div className="font-medium">{r.customer}</div>
                <div className="text-xs text-gray-500">{r.phone}</div>
              </td>
              <td className="px-4 py-3">
                <div className="flex items-center gap-2">
                  <img src="https://dummyimage.com/48x48/f2f2f2/aaa.png&text=🍛" className="size-10 rounded" />
                  <div>
                    <div className="font-medium">{r.itemTitle}</div>
                    <div className="text-xs text-gray-500 truncate max-w-[260px]">{r.itemSub}</div>
                  </div>
                </div>
              </td>
              <td className="px-4 py-3">
                <div className="font-medium">{r.branchCity}</div>
                <div className="text-xs text-gray-500">{r.branchState}</div>
              </td>
              <td className="px-4 py-3"><StatusPill status={r.status} /></td>
              <td className="px-4 py-3">
                <div className="font-medium">₹{r.amount}</div>
                <div className="text-xs text-gray-500">{r.payment}</div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function TabBtn({ active, onClick, children }: { active: boolean; onClick: () => void; children: React.ReactNode }) {
  return (
    <button
      onClick={onClick}
      className={`pb-2 transition-colors ${active ? 'text-gray-900 border-b-2 border-gray-900' : 'text-gray-500 hover:text-gray-700'}`}
    >
      {children}
    </button>
  )
}
