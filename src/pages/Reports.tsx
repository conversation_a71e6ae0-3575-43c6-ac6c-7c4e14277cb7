import PageShell from '../components/PageShell'
import SectionCard from '../components/SectionCard'
import StatCard from '../components/StatCard'

import AreaLine from '../components/charts/AreaLine'
import Donut from '../components/charts/Donut'
import Bar from '../components/charts/Bar'
import BarY from '../components/charts/BarY'
import { Link } from 'react-router-dom'
import React, { useEffect, useState } from 'react'

// ---- demo data (replace with Supabase later) ----
const days = ['Jul 1', 'Jul 3', 'Jul 5', 'Jul 7', 'Jul 9', 'Jul 11', 'Jul 12']
const lineDemo = [45, 90, 70, 30, 65, 95, 35]
const conversionBars = [9782, 4329, 3019, 2847]
const byProduct = ['<PERSON><PERSON><PERSON>un (6pc)', 'Bucket Chicken Biryani', 'Chicken 65 (6pc)', 'Chicken Biryani', 'Chicken Biryani Bucket Combo']
const byProductData = [120, 180, 140, 200, 150]
const sellThroughLabels = ['Soft Drink Pet Bottle', 'Veg Biryani Combo – Serve 10', 'Bucket Chicken Biryani', 'Bucket Combos', 'Biryani']
const sellThroughData = [20.47, 16.95, 7.59, 6.11, 4.95]
const deviceCounts = [10831, 2719, 182]

// list used for the left submenu
const sections = [
    { id: 'orders-returns-product', title: 'Orders & Returns by Product' },
    { id: 'avg-order-value', title: 'Average Order Value Over Time' },
    { id: 'order-over-time', title: 'Order Over Time' },
    { id: 'conversion-breakdown', title: 'Conversion Rate Breakdown' },
    { id: 'session-over-time', title: 'Session Over Time' },
    { id: 'sales-by-channel', title: 'Total Sales by Sales Channel' },
    { id: 'sales-over-time', title: 'Total Sales Over Time (Table)' },
    { id: 'returning-rate', title: 'Returning Customer Rate Over Time' },
    { id: 'bounce-rate', title: 'Bounce Rate' },
    { id: 'checkout-rate', title: 'Checkout Conversion Rate Over Time' },
    { id: 'new-vs-returning', title: 'New vs Returning Customer Sales' },
    { id: 'fulfilled-over-time', title: 'Order Fulfilled Over Time' },
    { id: 'sell-through', title: 'Product by Sell‑through Rate' },
    { id: 'sales-by-referrer', title: 'Total Sales by Referrer' },
    { id: 'sales-by-market', title: 'Sales Attributed to Market' },
    { id: 'search-conversions', title: 'Search Conversions Over Time' },
    { id: 'search-query', title: 'Search Query' },
    { id: 'sessions-marketing', title: 'Sessions Attributed to Marketing Campaigns' },
    { id: 'sessions-device', title: 'Sessions by Device Type' },
    { id: 'sessions-landing', title: 'Sessions by Landing Page' },
    { id: 'sessions-location', title: 'Sessions by Location' },
    { id: 'sessions-referrer', title: 'Sessions by Referrer' },
    { id: 'sessions-social', title: 'Sessions by Social Referrer' },
    { id: 'sales-breakdown', title: 'Total Sales Breakdown (Summary)' },
] as const

export default function Reports() {
    // track which section is in view to highlight submenu
    const [active, setActive] = useState<string>(sections[0].id)
    useEffect(() => {
        const obs = new IntersectionObserver(
            (entries) => {
                entries.forEach((e) => {
                    if (e.isIntersecting) setActive(e.target.id)
                })
            },
            { rootMargin: '-40% 0px -55% 0px', threshold: [0, 1] }
        )
        sections.forEach(s => {
            const el = document.getElementById(s.id)
            if (el) obs.observe(el)
        })
        return () => obs.disconnect()
    }, [])

    return (
        <PageShell title="Reports">
            <div className="grid grid-cols-12 gap-4">
                {/* Sticky submenu */}
                <aside className="col-span-12 lg:col-span-3 xl:col-span-2">
                    <div className="lg:sticky lg:top-20 bg-white rounded-2xl border shadow-sm p-3 max-h-[80vh] overflow-auto">
                        <div className="text-sm font-semibold px-2 pb-2">Reports</div>
                        <nav className="space-y-1">
                            {sections.map(s => (
                                <a
                                    key={s.id}
                                    href={`#${s.id}`}
                                    className={`block rounded-lg px-3 py-2 text-sm ${active === s.id ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50'
                                        }`}
                                >
                                    {s.title}
                                </a>
                            ))}
                        </nav>
                    </div>
                </aside>

                {/* Main content */}
                <div className="col-span-12 lg:col-span-9 xl:col-span-10 space-y-4">
                    {/* Top stats row (optional) */}
                    <div className="grid gap-4 sm:grid-cols-2 xl:grid-cols-4">
                        <StatCard title="Total Orders" value={312} />
                        <StatCard title="Preparing" value={87} />
                        <StatCard title="Delivered" value={39} />
                        <StatCard title="Cancelled" value={10} />
                    </div>

                    {/* Row 1 */}
                    <div className="grid gap-4 xl:grid-cols-2">
                        <SectionCard id="orders-returns-product" title="Orders and Returns by Product" actionText="View Details"
                            onAction={undefined}>
                            <div className="flex justify-end -mt-2 -mb-1">
                                <Link className="text-sm text-blue-600" to="/reports/orders-returns-product">View Details</Link>
                            </div>
                            <Bar labels={byProduct} dataPoints={byProductData} />
                        </SectionCard>
                        <SectionCard id="avg-order-value" title="Average Order Value Over Time">
                            <AreaLine labels={days} datasets={[{ label: 'AOV', data: lineDemo }]} />
                        </SectionCard>
                    </div>

                    {/* Row 2 */}
                    <div className="grid gap-4 xl:grid-cols-2">
                        <SectionCard id="order-over-time" title="Order Over Time">
                            <AreaLine labels={days} datasets={[{ label: 'Orders', data: [5, 9, 4, 3, 10, 2, 6] }]} />
                        </SectionCard>
                        <SectionCard id="conversion-breakdown" title="Conversion Rate Breakdown">
                            <Bar
                                labels={['Sessions', 'Add to cart', 'Reached Checkout', 'Completed Checkout']}
                                dataPoints={conversionBars}
                            />
                        </SectionCard>
                    </div>

                    {/* Row 3 */}
                    <div className="grid gap-4 xl:grid-cols-2">
                        <SectionCard id="session-over-time" title="Session Over Time">
                            <AreaLine labels={days} datasets={[{ label: 'Sessions', data: [220, 520, 300, 140, 480, 520, 300] }]} />
                        </SectionCard>
                        <SectionCard id="sales-by-channel" title="Total Sales by Sales Channel">
                            <Donut labels={['Online Store']} dataPoints={[475000]} centerText="₹475K" />
                        </SectionCard>
                    </div>

                    {/* Sales over time table */}
                    <SectionCard id="sales-over-time" title="Total Sales Over Time">
                        <div className="overflow-x-auto">
                            <table className="min-w-full text-sm">
                                <thead className="text-gray-500">
                                    <tr>
                                        <th className="text-left px-4 py-2">Day</th>
                                        <th className="text-left px-4 py-2">Net Sales</th>
                                        <th className="text-left px-4 py-2">Shipping Charges</th>
                                        <th className="text-left px-4 py-2">Duties</th>
                                        <th className="text-left px-4 py-2">Additional Fees</th>
                                        <th className="text-left px-4 py-2">Taxes</th>
                                        <th className="text-left px-4 py-2">Total Sales</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {Array.from({ length: 8 }).map((_, i) => (
                                        <tr key={i} className="border-t">
                                            <td className="px-4 py-2">Jun {i + 1}, 2025</td>
                                            <td className="px-4 py-2">₹4,20,288.00</td>
                                            <td className="px-4 py-2">₹90.00</td>
                                            <td className="px-4 py-2">₹0.00</td>
                                            <td className="px-4 py-2">₹35</td>
                                            <td className="px-4 py-2">₹10,000.00</td>
                                            <td className="px-4 py-2">₹4,30,378.00</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </SectionCard>

                    {/* Row 4 */}
                    <div className="grid gap-4 xl:grid-cols-2">
                        <SectionCard id="returning-rate" title="Returning Customer Rate Over Time">
                            <AreaLine labels={days} datasets={[{ label: 'Returning %', data: [40, 55, 32, 60, 35, 78, 28] }]} />
                        </SectionCard>
                        <SectionCard id="bounce-rate" title="Bounce Rate">
                            <AreaLine labels={days} datasets={[{ label: 'Bounce %', data: [35, 60, 45, 30, 55, 85, 25] }]} />
                        </SectionCard>
                    </div>

                    <div className="grid gap-4 xl:grid-cols-2">
                        <SectionCard id="checkout-rate" title="Checkout Conversion Rate Over Time">
                            <AreaLine labels={days} datasets={[{ label: 'Checkout %', data: [30, 60, 50, 25, 60, 90, 30] }]} />
                        </SectionCard>
                        <SectionCard id="new-vs-returning" title="New vs Returning Customer Sales">
                            <AreaLine labels={days} datasets={[
                                { label: 'Returning', data: [22, 65, 40, 60, 90, 70, 80] },
                                { label: 'New', data: [12, 25, 18, 28, 42, 35, 45] },
                            ]} />
                        </SectionCard>
                    </div>

                    <div className="grid gap-4 xl:grid-cols-2">
                        <SectionCard id="fulfilled-over-time" title="Order Fulfilled Over Time">
                            <AreaLine labels={days} datasets={[{ label: 'Fulfilled', data: [40, 90, 85, 30, 45, 95, 35] }]} />
                        </SectionCard>
                        <SectionCard id="sell-through" title="Product by Sell-through Rate">
                            <BarY labels={sellThroughLabels} dataPoints={sellThroughData} />
                        </SectionCard>
                    </div>

                    <div className="grid gap-4 xl:grid-cols-2">
                        <SectionCard id="sales-by-referrer" title="Total Sales by Referrer">
                            <BarY labels={['None - Manis', 'Search - Google', 'None - OTPless', 'None - None']} dataPoints={[29120, 8783, 7411, 2187]} />
                        </SectionCard>
                        <SectionCard id="sales-by-market" title="Sales Attributed to Market">
                            <BarY labels={['OTPless', 'Facebook', 'Razorpay', 'Other']} dataPoints={[74728, 50191, 22910, 12000]} />
                        </SectionCard>
                    </div>

                    <div className="grid gap-4 xl:grid-cols-2">
                        <SectionCard id="search-conversions" title="Search Conversions Over Time">
                            <AreaLine labels={days} datasets={[{ label: 'Search Conv %', data: lineDemo }]} />
                        </SectionCard>
                        <SectionCard id="search-query" title="Search Query">
                            <BarY labels={['Paneer', 'IPL', 'Biryani Rice', 'Biryani', 'Egg Biryani']} dataPoints={[6, 3, 2, 1, 1]} />
                        </SectionCard>
                    </div>

                    <div className="grid gap-4 xl:grid-cols-2">
                        <SectionCard id="sessions-marketing" title="Session Attributed to Marketing Campaigns">
                            <BarY labels={['Facebook', 'Instagram', 'Google Search', 'Meta']} dataPoints={[3302, 2719, 1467, 61]} />
                        </SectionCard>
                        <SectionCard id="sessions-device" title="Sessions by Device Type">
                            <Donut labels={['Mobile', 'Desktop', 'Tablet']} dataPoints={deviceCounts} centerText="21.8K" />
                        </SectionCard>
                    </div>

                    <div className="grid gap-4 xl:grid-cols-2">
                        <SectionCard id="sessions-landing" title="Sessions by Landing Page">
                            <BarY labels={[
                                'Collection /collection/party-menu',
                                'Product /collection/party-menu/products/bucket-chicken-biryani',
                                'Product /products/bucket-chicken-biryani-party-1',
                                'Product /products/chicken-biryani-boneless'
                            ]} dataPoints={[4452, 1092, 628, 209]} />
                        </SectionCard>
                        <SectionCard id="sessions-location" title="Sessions by Location">
                            <BarY labels={[
                                'India • Tamil Nadu • Chennai',
                                'India • Karnataka • Chennai',
                                'India • None • None',
                                'India • Maharashtra • Mumbai'
                            ]} dataPoints={[3302, 2719, 1267, 961]} />
                        </SectionCard>
                    </div>

                    <div className="grid gap-4 xl:grid-cols-2">
                        <SectionCard id="sessions-referrer" title="Sessions by Referrer">
                            <BarY labels={['Direct • None • Chennai', 'Social • Instagram • Chennai', 'Direct • None • Bengaluru', 'Social • Facebook • Chennai']} dataPoints={[3102, 2719, 1267, 961]} />
                        </SectionCard>
                        <SectionCard id="sessions-social" title="Sessions by Social Referrer">
                            <BarY labels={['Instagram', 'Facebook', 'Google']} dataPoints={[2081, 1829, 961]} />
                        </SectionCard>
                    </div>

                    <SectionCard id="sales-breakdown" title="Total Sales Breakdown">
                        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-3 text-sm">
                            <BreakRow label="Gross Sales" value="₹654,629.03" />
                            <BreakRow label="Discounts" value="‑₹66,462.30" />
                            <BreakRow label="Returns" value="‑₹1,927.61" />
                            <BreakRow label="Net Sales" value="₹700.00" />
                            <BreakRow label="Shipping Charges" value="₹700.00" />
                            <BreakRow label="Return Fees" value="₹700.00" />
                            <BreakRow label="Taxes" value="₹29,103.02" />
                            <BreakRow label="Total Sales" value="₹625,193.51" strong />
                        </div>
                    </SectionCard>
                </div>
            </div>
        </PageShell>
    )
}

function BreakRow({ label, value, strong = false }: { label: string; value: string; strong?: boolean }) {
    return (
        <div className={`flex items-center justify-between bg-gray-50 rounded-xl px-3 py-2 ${strong ? 'font-semibold' : ''}`}>
            <span className="text-gray-600">{label}</span>
            <span>{value}</span>
        </div>
    )
}
