import { useEffect, useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { useLocation, useNavigate } from 'react-router-dom'

export default function Login() {
  const { user, signIn, signUp } = useAuth()
  const nav = useNavigate()
  const loc = useLocation() as any
  const from = loc.state?.from?.pathname ?? '/'

  const [mode, setMode] = useState<'signin'|'signup'>('signin')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [err, setErr] = useState<string | null>(null)
  const [submitting, setSubmitting] = useState(false)

  useEffect(() => { if (user) nav(from, { replace: true }) }, [user, from, nav])

  const submit = async (e: React.FormEvent) => {
    e.preventDefault()
    setErr(null); setSubmitting(true)
    try {
      if (mode === 'signin') {
        await signIn(email, password)
      } else {
        await signUp(email, password)
        // optional: auto sign-in if email confirmations are disabled
        await signIn(email, password).catch(()=>{})
      }
    } catch (e: any) {
      setErr(e?.message ?? 'Something went wrong')
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen grid place-items-center bg-gray-50">
      <div className="w-full max-w-md bg-white border rounded-2xl shadow-sm p-6">
        <div className="mb-4 flex gap-2">
          <button
            className={`flex-1 rounded-xl py-2 ${mode==='signin'?'bg-gray-900 text-white':'bg-gray-100'}`}
            onClick={() => setMode('signin')}
          >Sign in</button>
          <button
            className={`flex-1 rounded-xl py-2 ${mode==='signup'?'bg-gray-900 text-white':'bg-gray-100'}`}
            onClick={() => setMode('signup')}
          >Create account</button>
        </div>

        <form className="space-y-3" onSubmit={submit}>
          <div>
            <label className="block text-sm text-gray-600">Email</label>
            <input
              className="mt-1 w-full rounded-xl"
              type="email" placeholder="<EMAIL>"
              value={email} onChange={e=>setEmail(e.target.value)} required
            />
          </div>
          <div>
            <label className="block text-sm text-gray-600">Password</label>
            <input
              className="mt-1 w-full rounded-xl"
              type="password" placeholder="••••••••"
              value={password} onChange={e=>setPassword(e.target.value)} required
            />
          </div>
          {err && <p className="text-xs text-red-600">{err}</p>}
          <button disabled={submitting} className="w-full rounded-xl bg-brand-500 text-white py-2">
            {submitting ? 'Please wait…' : (mode==='signin' ? 'Sign in' : 'Create account')}
          </button>
          <p className="text-xs text-gray-500 text-center">
            Tip: enable <b>Email + Password</b> in Supabase → Authentication.
          </p>
        </form>
      </div>
    </div>
  )
}
