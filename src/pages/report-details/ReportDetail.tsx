import { useParams, useNavigate } from 'react-router-dom'
import PageShell from '../../components/PageShell'
import SectionCard from '../../components/SectionCard'
import Bar from '../../components/charts/Bar'
import BarY from '../../components/charts/BarY'
import AreaLine from '../../components/charts/AreaLine'
import Donut from '../../components/charts/Donut'
import React, { useMemo, useState } from 'react'

// --- map each slug to a renderer & title
const configs = {
  'orders-returns-product': {
    title: 'Orders and Returns by Product',
    render: () => <Bar labels={demo.byProduct.labels} dataPoints={demo.byProduct.values} />,
    table: { columns: ['Product Title','Quantity Order','Quantity Returned','Returned Quantity Rate'] as const },
  },
  'avg-order-value': {
    title: 'Average Order Value Over Time',
    render: () => <AreaLine labels={demo.days} datasets={[{ label:'AOV', data: demo.line }]} />,
    table: null,
  },
  // add more slugs here as you wire them…
} as const

type Slug = keyof typeof configs

export default function ReportDetail() {
  const { slug = '' } = useParams()
  const navigate = useNavigate()
  const cfg = configs[slug as Slug]

  // fallback if wrong slug
  if (!cfg) return <PageShell title="Report not found"><div className="p-6">Invalid report.</div></PageShell>

  // pagination demo state
  const [page, setPage] = useState(1)
  const pageSize = 10

  // demo rows (replace with Supabase)
  const rows = useMemo(() => demo.tableRows, [])
  const paged = rows.slice((page-1)*pageSize, page*pageSize)
  const pages = Math.ceil(rows.length / pageSize)

  return (
    <PageShell
      title={cfg.title}
      actions={
        <div className="flex items-center gap-2">
          <select className="rounded-xl border-gray-200"><option>This month</option></select>
          <select className="rounded-xl border-gray-200"><option>Bangalore</option></select>
          <select className="rounded-xl border-gray-200"><option>Whitefield, IN</option></select>
        </div>
      }
    >
      <div className="grid grid-cols-12 gap-4">
        {/* Filters panel */}
        <aside className="col-span-12 lg:col-span-3">
          <FiltersPanel />
        </aside>

        {/* Main */}
        <div className="col-span-12 lg:col-span-9 space-y-4">
          <button onClick={() => navigate(-1)} className="text-sm text-gray-600">&larr; Back</button>

          <SectionCard title={cfg.title} actionText="">
            {cfg.render()}
          </SectionCard>

          {/* “Orders & Returns by Product” table like your screenshot */}
          {slug === 'orders-returns-product' && (
            <section className="bg-white rounded-2xl border shadow-sm">
              <div className="px-4 py-3 border-b text-sm text-gray-600 flex items-center justify-between">
                <div className="font-medium">Summary</div>
                <div className="flex gap-8">
                  <span>Quantity Order: <strong>830</strong></span>
                  <span>Quantity Returned: <strong>0</strong></span>
                  <span>Returned Quantity Rate: <strong>0.12%</strong></span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full text-sm">
                  <thead className="text-gray-500">
                    <tr>
                      <th className="text-left px-4 py-2">Product Title</th>
                      <th className="text-left px-4 py-2">Quantity Order ⭥</th>
                      <th className="text-left px-4 py-2">Quantity Returned</th>
                      <th className="text-left px-4 py-2">Returned Quantity Rate</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paged.map(r => (
                      <tr key={r.id} className="border-t">
                        <td className="px-4 py-2">
                          <div className="flex items-center gap-2">
                            <img src={r.img} className="size-6 rounded" />
                            <span>{r.product}</span>
                          </div>
                        </td>
                        <td className="px-4 py-2">{r.qty}</td>
                        <td className="px-4 py-2">{r.ret}</td>
                        <td className="px-4 py-2">{r.rate}%</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* pagination */}
              <div className="flex items-center justify-center gap-2 py-3">
                <button className="px-2 py-1 rounded-lg bg-gray-100" disabled={page===1} onClick={()=>setPage(p=>p-1)}>&larr;</button>
                {Array.from({length: pages}).map((_,i)=>(
                  <button
                    key={i}
                    onClick={()=>setPage(i+1)}
                    className={`size-8 rounded-lg ${page===i+1?'bg-gray-900 text-white':'bg-gray-100'}`}
                  >{i+1}</button>
                ))}
                <button className="px-2 py-1 rounded-lg bg-gray-100" disabled={page===pages} onClick={()=>setPage(p=>p+1)}>&rarr;</button>
              </div>
            </section>
          )}
        </div>
      </div>
    </PageShell>
  )
}

/* ---------------- Filters panel ---------------- */
function FiltersPanel() {
  return (
    <div className="bg-white rounded-2xl border shadow-sm p-3">
      <div className="relative mb-3">
        <input placeholder="Search" className="w-full rounded-xl border-gray-200 pl-9" />
        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">⌘K</span>
      </div>

      <Accordion title="SALE">
        {[
          'Active subscriptions','Additional fees','Additional product & order discount',
          'Additional shipping discount','Amount spent per customer','Applied discounts',
        ].map(k => <CheckRow key={k} label={k} />)}
      </Accordion>

      <Accordion title="SESSION">
        {['Device type','Referrer','Landing page'].map(k => <CheckRow key={k} label={k} />)}
      </Accordion>

      <Accordion title="ORDER">
        {[
          'Average shipping label cost','First click average order quantity','First click average order value',
          'First click net items sold','First click order count','First click sales','Last click average order quantity',
        ].map(k => <CheckRow key={k} label={k} />)}
      </Accordion>
    </div>
  )
}

function Accordion({ title, children }: { title: string; children: React.ReactNode }) {
  const [open, setOpen] = React.useState(true)
  return (
    <div className="border rounded-xl mb-2">
      <button onClick={()=>setOpen(o=>!o)} className="w-full flex items-center justify-between px-3 py-2">
        <span className="text-sm font-semibold">{title}</span>
        <span className="text-gray-500">{open ? '–' : '+'}</span>
      </button>
      {open && <div className="px-3 pb-2 space-y-2">{children}</div>}
    </div>
  )
}
function CheckRow({ label }: { label: string }) {
  return (
    <label className="flex items-center gap-2 text-sm text-gray-700">
      <input type="checkbox" className="rounded" /> {label}
    </label>
  )
}

/* ---------------- demo data (swap with Supabase) ---------------- */
const demo = {
  days: ['Jul 1','Jul 3','Jul 5','Jul 7','Jul 9','Jul 11','Jul 12'],
  line: [45,90,70,30,65,95,35],
  byProduct: {
    labels: ['Gulab Jamun (1pc)','Bucket Chicken Biryani','Chicken 65 (4pc)','Chicken Biryani','Chicken Biryani Bucket Combos'],
    values: [55,60,80,85,58],
  },
  tableRows: Array.from({length: 50}).map((_,i)=>({
    id: i+1,
    img: 'https://dummyimage.com/40x40/f2f2f2/aaa.png&text=🍗',
    product: [
      'Gulab Jamun (1pc)','Bucket Chicken Biryani','Chicken 65 (4pc)','Chicken Biryani',
      'Chicken Biryani Bucket Combo','Veg Biryani','Bucket Veg Biryani','Soft Drink Pet Bottle',
      'Bucket Chicken Biryani Boneless','Paneer Chilli'
    ][i%10],
    qty: 35, ret: 0, rate: 0,
  })),
}
